/**
 * 应用配置常量
 */

// JWT Token 配置
export const JWT_CONFIG = {
  // 默认的JWT token（开发环境使用）
  DEFAULT_TOKEN:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjdWJlLXN0dWRpbyIsInN1YiI6ImFkbWluIiwiaWF0IjoxNzUzNDA3NzI2LCJleHAiOjE3NTM0OTQxMjYsInVzZXJfaWQiOjEsInJvbGVzIjpbIkFkbWluIl19.UHWOAniL0Y9AVaz6wCnzJ0R3H_kWz2uk-FypDK_2hUA',
  // localStorage中存储token的key
  TOKEN_KEY: 'myapp_token',
} as const;

// API 配置
export const API_CONFIG = {
  // 请求超时时间（毫秒）
  TIMEOUT: 10000,
  // 响应类型
  RESPONSE_TYPE: 'json',
  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  },
} as const;

/**
 * 获取Authorization header的值
 * 优先从localStorage获取，如果没有则使用默认token
 */
export function getAuthorizationHeader(): string {
  return localStorage.getItem(JWT_CONFIG.TOKEN_KEY) || JWT_CONFIG.DEFAULT_TOKEN;
}

/**
 * 设置token到localStorage
 */
export function setToken(token: string): void {
  localStorage.setItem(JWT_CONFIG.TOKEN_KEY, token);
}

/**
 * 清除token
 */
export function clearToken(): void {
  localStorage.removeItem(JWT_CONFIG.TOKEN_KEY);
}

/**
 * 从 pipelineParams 中获取 token
 * @param pipelineParamsStr - URL参数中的pipelineParams字符串
 * @returns token字符串，如果解析失败或不存在则返回空字符串
 */
export function getTokenFromPipelineParams(pipelineParamsStr?: string): string {
  if (!pipelineParamsStr) {
    return '';
  }

  try {
    const pipelineParamsObj = JSON.parse(decodeURIComponent(pipelineParamsStr));
    return pipelineParamsObj.token || '';
  } catch (error) {
    console.warn('解析pipelineParams中的token失败:', error);
    return '';
  }
}

/**
 * 获取完整的Authorization header值
 * 优先级：pipelineParams中的token > localStorage中的token > 默认token
 * @param pipelineParamsStr - URL参数中的pipelineParams字符串
 */
export function getAuthorizationHeaderWithPipelineParams(pipelineParamsStr?: string): string {
  // 首先尝试从pipelineParams获取token
  const pipelineToken = getTokenFromPipelineParams(pipelineParamsStr);
  if (pipelineToken) {
    return pipelineToken;
  }

  // 如果pipelineParams中没有token，则使用原有逻辑
  return getAuthorizationHeader();
}
